import datetime
import os
import pickle
import time
import hashlib
import numpy as np
import pandas as pd
import tushare as ts
from tqdm import tqdm  # 用于显示进度

import fire
from functools import wraps
import argparse
from configs import init_config, config_info
from util import load_stock_data

# 导入因子分析所需的库
try:
    import alphalens
    import alphalens.utils
    import alphalens.performance
    from alphalens.tears import create_returns_tear_sheet
    import matplotlib.pyplot as plt
    from PIL import Image, ImageChops
    ALPHALENS_AVAILABLE = True
except ImportError as e:
    print(f"警告：无法导入alphalens相关库: {e}")
    print("请安装alphalens: pip install alphalens")
    ALPHALENS_AVAILABLE = False


class FactorCalculator:
    """
    因子计算类，用于计算规模、贝塔、质量、成长和价值因子。
    同时对因子数据做预处理（缺失值填补、异常值处理、标准化、衍生指标生成和平滑处理），
    以适合后续 AutoGluon 模型预测未来 10 天股价。
    """

    def __init__(self, token: str, cache_dir: str = './cache', max_cache_size: int = 1000):
        """
        初始化函数，设置 tushare 接口、缓存目录及接口调用速率限制

        Args:
            token: tushare API token
            cache_dir: 缓存数据存储路径
            max_cache_size: 最大缓存条目数，超过时会清理最旧的缓存
        """
        # 禁用代理设置
        import os
        import urllib3
        
        # 禁用所有可能的代理环境变量
        proxy_env_vars = ['http_proxy', 'https_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 
                         'ftp_proxy', 'FTP_PROXY', 'all_proxy', 'ALL_PROXY',
                         'no_proxy', 'NO_PROXY']
        for var in proxy_env_vars:
            if var in os.environ:
                del os.environ[var]
        
        # 禁用urllib3的代理警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # 设置tushare token并确保不使用代理
        ts.set_token(token)
        os.environ['NO_PROXY'] = '*'
        self.pro = ts.pro_api()
        self.pro.session.trust_env = False
        # 尝试设置requests会话不使用代理
        try:
            import requests
            session = requests.Session()
            session.proxies = {}
            # 如果tushare支持自定义session，使用它
            if hasattr(self.pro, '_session'):
                self.pro._session = session
        except Exception as e:
            print(f"设置requests会话时出错: {e}，将继续使用默认设置")

        self.cache_dir = cache_dir
        os.makedirs(self.cache_dir, exist_ok=True)

        self.last_request_time = 0
        self.request_interval = 0.01

        self.cached_data = {}
        self.cache_access_time = {}  # 记录缓存访问时间，用于LRU清理
        self.max_cache_size = max_cache_size
        self.load_cache()

    def _is_cache_enabled(self):
        """
        检查是否启用缓存策略
        当环境变量use_cache设置为true、1、True等值时启用缓存
        """
        use_cache = os.environ.get('use_cache', '').lower()
        return use_cache in ['true', '1', 'yes', 'on', 'enabled']

    def _retry_pro_api_call(self, func, *args, **kwargs):
        """
        重试机制封装pro.xxx函数调用
        重试间隔：1, 5, 10, 20, 20, 20... 秒
        """
        retry_intervals = [1, 5, 10] + [20] * 10  # 前3次用递增间隔，后续都用20秒

        for attempt, interval in enumerate(retry_intervals):
            try:
                self._rate_limit()  # 应用速率限制
                result = func(*args, **kwargs)
                if attempt > 0:
                    print(f"✓ API调用在第{attempt + 1}次尝试后成功")
                return result
            except Exception as e:
                print(f"✗ API调用失败 (尝试 {attempt + 1}/{len(retry_intervals)}): {e}")

                if attempt == len(retry_intervals) - 1:
                    # 最后一次尝试失败
                    print(f"✗ API调用最终失败，已重试{len(retry_intervals)}次")
                    raise e

                print(f"⏳ {interval}秒后重试...")
                time.sleep(interval)

        # 理论上不会到达这里，但为了安全起见
        raise Exception("重试机制异常结束")

    def load_cache(self):
        """加载本地缓存数据"""
        if not self._is_cache_enabled():
            print("缓存功能已禁用（环境变量use_cache未设置为true）")
            return
            
        cache_file = os.path.join(self.cache_dir, 'factor_cache.pkl')
        access_time_file = os.path.join(self.cache_dir, 'cache_access_time.pkl')
        
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    loaded_data = pickle.load(f)
                
                # 兼容旧格式的缓存数据
                if loaded_data and isinstance(list(loaded_data.values())[0], pd.DataFrame):
                    # 旧格式：直接存储DataFrame
                    print("检测到旧格式缓存，正在转换...")
                    self.cached_data = {}
                    for key, df in loaded_data.items():
                        # 尝试从键中解析信息（可能不准确，但尽力而为）
                        parts = key.split('_')
                        if len(parts) >= 4:
                            data_type = parts[0]
                            ts_code = '_'.join(parts[1:-2])
                            start_date = parts[-2]
                            end_date = parts[-1]
                            
                            new_key = self._get_cache_key(data_type, ts_code, start_date, end_date)
                            self.cached_data[new_key] = {
                                'data_type': data_type,
                                'ts_code': ts_code,
                                'start_date': start_date,
                                'end_date': end_date,
                                'data': df
                            }
                            self.cache_access_time[new_key] = time.time()
                else:
                    # 新格式
                    self.cached_data = loaded_data
                    
                # 加载访问时间
                if os.path.exists(access_time_file):
                    with open(access_time_file, 'rb') as f:
                        self.cache_access_time = pickle.load(f)
                else:
                    # 如果没有访问时间文件，初始化为当前时间
                    current_time = time.time()
                    self.cache_access_time = {key: current_time for key in self.cached_data.keys()}
                        
            except Exception as e:
                print(f"加载缓存失败: {e}")
                self.cached_data = {}
                self.cache_access_time = {}

    def save_cache(self):
        """将缓存数据保存到本地文件"""
        if not self._is_cache_enabled():
            return
            
        cache_file = os.path.join(self.cache_dir, 'factor_cache.pkl')
        access_time_file = os.path.join(self.cache_dir, 'cache_access_time.pkl')
        
        try:
            # 保存缓存数据
            with open(cache_file, 'wb') as f:
                pickle.dump(self.cached_data, f)

            # 保存访问时间
            with open(access_time_file, 'wb') as f:
                pickle.dump(self.cache_access_time, f)

        except Exception as e:
            print(f"保存缓存失败: {e}")

    def _rate_limit(self):
        """API 调用速率限制，确保调用间隔不少于 request_interval 秒"""
        current_time = time.time()
        elapsed = current_time - self.last_request_time
        if elapsed < self.request_interval:
            time.sleep(self.request_interval - elapsed)
        self.last_request_time = time.time()

    def _get_cache_key(self, data_type: str, ts_code: str, start_date: str, end_date: str) -> str:
        """生成缓存键值，使用哈希避免键冲突"""
        key_str = f"{data_type}|{ts_code}|{start_date}|{end_date}"
        return hashlib.md5(key_str.encode()).hexdigest()

    def _get_cached_data(self, data_type: str, ts_code: str, start_date: str, end_date: str) -> (pd.DataFrame, str, str):
        """
        获取已缓存数据，并返回未缓存的日期范围

        Returns:
            已缓存数据 DataFrame，未缓存部分的起始日期和结束日期（字符串格式）
        """
        if not self._is_cache_enabled():
            # 如果缓存未启用，返回空DataFrame和完整的日期范围
            return pd.DataFrame(), start_date, end_date
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)

        cached_df = pd.DataFrame()
        covered_ranges = []

        # 查找所有相关的缓存数据
        for key, cache_info in self.cached_data.items():
            if cache_info['data_type'] == data_type and cache_info['ts_code'] == ts_code:
                cache_start = pd.to_datetime(cache_info['start_date'])
                cache_end = pd.to_datetime(cache_info['end_date'])

                # 检查是否有交集
                if not (cache_end < start_date or cache_start > end_date):
                    df = cache_info['data']
                    # 提取请求范围内的数据
                    mask = (df.index >= start_date) & (df.index <= end_date)
                    if mask.any():
                        if cached_df.empty:
                            cached_df = df[mask].copy()
                        else:
                            cached_df = pd.concat([cached_df, df[mask]])

                        # 记录已覆盖的范围
                        overlap_start = max(cache_start, start_date)
                        overlap_end = min(cache_end, end_date)
                        covered_ranges.append((overlap_start, overlap_end))

                    # 更新访问时间
                    self.cache_access_time[key] = time.time()

        # 去重并排序
        if not cached_df.empty:
            cached_df = cached_df[~cached_df.index.duplicated(keep='first')].sort_index()

        # 计算未覆盖的范围
        uncached_ranges = self._calculate_uncached_ranges(start_date, end_date, covered_ranges)

        if uncached_ranges:
            # 返回第一个未缓存的范围
            uncached_start = uncached_ranges[0][0].strftime('%Y%m%d')
            uncached_end = uncached_ranges[0][1].strftime('%Y%m%d')
        else:
            # 全部已缓存，返回一个无效的范围表示无需获取新数据
            uncached_start = (end_date + datetime.timedelta(days=1)).strftime('%Y%m%d')
            uncached_end = (start_date - datetime.timedelta(days=1)).strftime('%Y%m%d')

        return cached_df, uncached_start, uncached_end

    def _calculate_uncached_ranges(self, start_date, end_date, covered_ranges):
        """计算未被缓存覆盖的日期范围"""
        if not covered_ranges:
            return [(start_date, end_date)]
        
        # 合并重叠的范围
        covered_ranges.sort(key=lambda x: x[0])
        merged_ranges = [covered_ranges[0]]
        
        for current_start, current_end in covered_ranges[1:]:
            last_start, last_end = merged_ranges[-1]
            if current_start <= last_end + datetime.timedelta(days=1):
                # 合并重叠或相邻的范围
                merged_ranges[-1] = (last_start, max(last_end, current_end))
            else:
                merged_ranges.append((current_start, current_end))
        
        # 找出未覆盖的范围
        uncached_ranges = []
        current_pos = start_date
        
        for range_start, range_end in merged_ranges:
            if current_pos < range_start:
                uncached_ranges.append((current_pos, range_start - datetime.timedelta(days=1)))
            current_pos = max(current_pos, range_end + datetime.timedelta(days=1))
        
        if current_pos <= end_date:
            uncached_ranges.append((current_pos, end_date))
        
        return uncached_ranges

    def _add_to_cache(self, data_type: str, ts_code: str, start_date: str, end_date: str, data: pd.DataFrame):
        """添加数据到缓存，包含LRU清理机制"""
        if not self._is_cache_enabled():
            return
            
        cache_key = self._get_cache_key(data_type, ts_code, start_date, end_date)

        cache_info = {
            'data_type': data_type,
            'ts_code': ts_code,
            'start_date': start_date,
            'end_date': end_date,
            'data': data
        }

        self.cached_data[cache_key] = cache_info
        self.cache_access_time[cache_key] = time.time()

        # 检查缓存大小，必要时清理
        if len(self.cached_data) > self.max_cache_size:
            self._cleanup_cache()

        # 定期保存缓存
        if len(self.cached_data) % 50 == 0:
            self.save_cache()

    def _cleanup_cache(self):
        """清理最旧的缓存条目（LRU策略）"""
        if not self._is_cache_enabled():
            return

        if len(self.cached_data) <= self.max_cache_size:
            return

        # 按访问时间排序，删除最旧的条目
        sorted_keys = sorted(self.cache_access_time.keys(), key=lambda k: self.cache_access_time[k])
        keys_to_remove = sorted_keys[:len(self.cached_data) - self.max_cache_size + 100]  # 多删除一些避免频繁清理

        for key in keys_to_remove:
            if key in self.cached_data:
                del self.cached_data[key]
            if key in self.cache_access_time:
                del self.cache_access_time[key]

    def _align_financial_data_with_daily(self, financial_data: pd.DataFrame, daily_index: pd.DatetimeIndex) -> pd.DataFrame:
        """
        将财务数据与日线数据的日期索引对齐，避免前瞻性偏差

        核心逻辑：
        1. 财务数据使用ann_date作为索引（已在_fetch_financial_data中处理）
        2. 对于每个交易日，只能使用该日期之前已公告的最新财务数据
        3. 使用前向填充方法，确保财务数据在公告后持续有效直到下次更新

        Args:
            financial_data: 以ann_date为索引的财务数据
            daily_index: 日线数据的日期索引

        Returns:
            与日线数据日期对齐的财务数据
        """
        if financial_data.empty or daily_index.empty:
            return pd.DataFrame()

        # 创建一个新的DataFrame，索引为日线数据的日期
        aligned_financial = pd.DataFrame(index=daily_index)

        # 对财务数据的每一列进行时间对齐
        for col in financial_data.columns:
            if col in ['ts_code', 'end_date']:  # 跳过非数值列
                continue

            # 使用reindex with method='ffill'进行前向填充
            # 这确保了只有在ann_date之后才能使用该财务数据
            series_aligned = financial_data[col].reindex(
                daily_index,
                method='ffill',  # 前向填充：使用最近的已公告数据
                limit=None  # 不限制填充距离
            )

            aligned_financial[col] = series_aligned

        return aligned_financial

    def _fetch_daily_data(self, ts_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """从本地CSV文件获取个股日线行情数据"""
        try:
            # 提取股票代码的6位数字部分
            stock_code = ts_code.split('.')[0]  # 去掉后缀如.SZ, .SH
            
            # 构建CSV文件路径
            csv_file_path = os.path.join(f"{config_info['market']}_daily", f"{stock_code}.csv")
            
            if os.path.exists(csv_file_path):
                # 读取CSV文件
                df = pd.read_csv(csv_file_path)
                
                # 处理日期列 - 兼容'datetime'和'date'两种列名
                if 'datetime' in df.columns:
                    df['datetime'] = pd.to_datetime(df['datetime'])
                    df.set_index('datetime', inplace=True)
                elif 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    df.set_index('date', inplace=True)
                else:
                    raise ValueError(f"CSV文件中未找到'datetime'或'date'列，实际列名: {df.columns.tolist()}")
                
                # 重命名列以匹配原始格式
                column_mapping = {
                    'open': 'open',
                    'high': 'high', 
                    'low': 'low',
                    'close': 'close',
                    'volume': 'vol',
                    'amount': 'amount'
                }
                
                # 只保留需要的列并重命名
                available_columns = [col for col in column_mapping.keys() if col in df.columns]
                df = df[available_columns].rename(columns=column_mapping)
                
                # 添加ts_code列
                df['ts_code'] = ts_code
                
                # 筛选日期范围
                start_dt = pd.to_datetime(start_date)
                end_dt = pd.to_datetime(end_date)
                mask = (df.index >= start_dt) & (df.index <= end_dt)
                df = df[mask]
                
                df.sort_index(inplace=True)
                return df
            else:
                print(f"CSV文件不存在: {csv_file_path}")
                return pd.DataFrame()
        except Exception as e:
            print(f"从CSV文件获取日线数据失败: {e}")
            return pd.DataFrame()

    def _fetch_index_daily(self, index_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取指数日线行情数据，采用本地 CSV 数据"""
        try:
            df = pd.read_csv(f"{config_info['market']}.csv", index_col=0, parse_dates=True)
            df.reset_index(inplace=True)
            # 检查列名，兼容'date'和'datetime'两种格式
            if 'datetime' in df.columns:
                df.rename(columns={'datetime': 'trade_date'}, inplace=True)
            elif 'date' in df.columns:
                df.rename(columns={'date': 'trade_date'}, inplace=True)
            else:
                # 如果索引是日期，使用索引
                df.rename_axis('trade_date', inplace=True)
            df.set_index('trade_date', inplace=True)
            
            # 筛选日期范围
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            mask = (df.index >= start_dt) & (df.index <= end_dt)
            df = df[mask]
            
            df.sort_index(inplace=True)
            return df
        except Exception as e:
            print(f"获取指数日线数据失败: {e}")
            return pd.DataFrame()

    def _fetch_financial_data(self, ts_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取财务数据，合并主要指标、利润表、资产负债表和现金流量表

        重要：使用ann_date作为索引，确保避免前瞻性偏差
        只有在财务数据公告日期(ann_date)之后，该数据才能被使用
        """
        cached_df, uncached_start, uncached_end = self._get_cached_data('financial', ts_code, start_date, end_date)
        if not cached_df.empty and pd.to_datetime(uncached_start) > pd.to_datetime(uncached_end):
            return cached_df

        df_list = [cached_df] if not cached_df.empty else []
        try:
            fina_indicator = self._retry_pro_api_call(self.pro.fina_indicator, ts_code=ts_code, start_date=uncached_start, end_date=uncached_end)
            income = self._retry_pro_api_call(self.pro.income, ts_code=ts_code, start_date=uncached_start, end_date=uncached_end)
            balancesheet = self._retry_pro_api_call(self.pro.balancesheet, ts_code=ts_code, start_date=uncached_start, end_date=uncached_end)
            cashflow = self._retry_pro_api_call(self.pro.cashflow, ts_code=ts_code, start_date=uncached_start, end_date=uncached_end)

            if not fina_indicator.empty:
                fin_data = fina_indicator
                if not income.empty:
                    income_selected = income[['ts_code', 'ann_date', 'end_date', 'revenue', 'n_income']]
                    fin_data = pd.merge(fin_data, income_selected, on=['ts_code', 'end_date'], how='outer')
                if not balancesheet.empty:
                    bs_selected = balancesheet[['ts_code', 'ann_date', 'end_date', 'total_assets', 'total_liab',
                                                'total_hldr_eqy_exc_min_int']]
                    fin_data = pd.merge(fin_data, bs_selected, on=['ts_code', 'end_date'], how='outer')
                if not cashflow.empty:
                    cf_selected = cashflow[['ts_code', 'end_date', 'n_cashflow_act']]
                    fin_data = pd.merge(fin_data, cf_selected, on=['ts_code', 'end_date'], how='outer')

                # 关键修复：使用公告日期作为索引，确保时间一致性
                # 只有在ann_date当日及之后，该财务数据才能被使用
                fin_data = fin_data.dropna(subset=['ann_date'])
                fin_data['ann_date'] = pd.to_datetime(fin_data['ann_date'])

                # 添加信息传播延迟：在公告日后1个交易日才能使用数据
                # 这更符合实际情况，因为公告通常在收盘后发布
                fin_data['ann_date'] = fin_data['ann_date'] + pd.Timedelta(days=1)

                fin_data = fin_data.sort_values('ann_date').set_index('ann_date')
                df_list.append(fin_data)
                self._add_to_cache('financial', ts_code, uncached_start, uncached_end, fin_data)
        except Exception as e:
            print(f"获取{ts_code}财务数据失败: {e}")

        if df_list:
            result_df = pd.concat(df_list)
            result_df = result_df[~result_df.index.duplicated(keep='first')].sort_index()
            return result_df
        return pd.DataFrame()

    def _fetch_daily_basic(self, ts_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取每日基本指标数据"""
        cached_df, uncached_start, uncached_end = self._get_cached_data('daily_basic', ts_code, start_date, end_date)
        if not cached_df.empty and pd.to_datetime(uncached_start) > pd.to_datetime(uncached_end):
            return cached_df

        df_list = [cached_df] if not cached_df.empty else []
        try:
            df = self._retry_pro_api_call(self.pro.daily_basic, ts_code=ts_code, start_date=uncached_start, end_date=uncached_end)
            if not df.empty:
                df['trade_date'] = pd.to_datetime(df['trade_date'])
                df = df.set_index('trade_date').sort_index()
                df_list.append(df)
                self._add_to_cache('daily_basic', ts_code, uncached_start, uncached_end, df)
        except Exception as e:
            print(f"获取每日指标数据失败: {e}")

        if df_list:
            result_df = pd.concat(df_list)
            result_df = result_df[~result_df.index.duplicated(keep='first')].sort_index()
            return result_df
        return pd.DataFrame()

    def _fetch_stk_factor_pro(self, ts_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取股票因子专业版数据"""
        cached_df, uncached_start, uncached_end = self._get_cached_data('stk_factor_pro', ts_code, start_date, end_date)
        if not cached_df.empty and pd.to_datetime(uncached_start) > pd.to_datetime(uncached_end):
            return cached_df

        df_list = [cached_df] if not cached_df.empty else []
        try:
            df = self._retry_pro_api_call(self.pro.stk_factor_pro, ts_code=ts_code, start_date=uncached_start, end_date=uncached_end,
                                        fields="ts_code,trade_date,pe,pe_ttm,pb,ps,ps_ttm,dv_ratio,dv_ttm,free_share,total_mv,circ_mv")
            if not df.empty:
                df['trade_date'] = pd.to_datetime(df['trade_date'])
                df = df.set_index('trade_date').sort_index()
                df_list.append(df)
                self._add_to_cache('stk_factor_pro', ts_code, uncached_start, uncached_end, df)
        except Exception as e:
            print(f"获取股票因子专业版数据失败: {e}")

        if df_list:
            result_df = pd.concat(df_list)
            result_df = result_df[~result_df.index.duplicated(keep='first')].sort_index()
            return result_df
        return pd.DataFrame()

    def _fetch_cyq_perf(self, ts_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取筹码分布数据"""
        cached_df, uncached_start, uncached_end = self._get_cached_data('cyq_perf', ts_code, start_date, end_date)
        if not cached_df.empty and pd.to_datetime(uncached_start) > pd.to_datetime(uncached_end):
            return cached_df

        df_list = [cached_df] if not cached_df.empty else []
        try:
            df = self._retry_pro_api_call(self.pro.cyq_perf, ts_code=ts_code, start_date=uncached_start, end_date=uncached_end,
                                  fields='ts_code,trade_date,cost_5pct,cost_15pct,cost_50pct,cost_85pct,cost_95pct,weight_avg,winner_rate')
            if not df.empty:
                df['trade_date'] = pd.to_datetime(df['trade_date'])
                df = df.set_index('trade_date').sort_index()
                df_list.append(df)
                self._add_to_cache('cyq_perf', ts_code, uncached_start, uncached_end, df)
        except Exception as e:
            print(f"获取筹码分布数据失败: {e}")

        if df_list:
            result_df = pd.concat(df_list)
            result_df = result_df[~result_df.index.duplicated(keep='first')].sort_index()
            return result_df
        return pd.DataFrame()

    # -------------------- 因子计算方法 -------------------- #

    def calculate_size_factors(self, daily_basic: pd.DataFrame) -> pd.DataFrame:
        """
        计算规模因子（仅保留对数形式，用作中性化控制项）：
          - ln_total_mv: 总市值对数
          - ln_circ_mv: 流通市值对数
        """
        if daily_basic.empty:
            return pd.DataFrame()

        size_factors = pd.DataFrame()
        if 'total_mv' in daily_basic.columns:
            total_mv_clean = pd.to_numeric(daily_basic['total_mv'], errors='coerce')
            size_factors['ln_total_mv'] = np.log(total_mv_clean.replace(0, np.nan))
        if 'circ_mv' in daily_basic.columns:
            circ_mv_clean = pd.to_numeric(daily_basic['circ_mv'], errors='coerce')
            size_factors['ln_circ_mv'] = np.log(circ_mv_clean.replace(0, np.nan))

        return size_factors

    def calculate_beta_factors(self, stock_daily: pd.DataFrame, index_daily: pd.DataFrame,
                               window: int = 60) -> pd.DataFrame:
        """
        计算波动/风险因子，包括：
          - beta: 滚动窗口内的贝塔系数
          - idiosyncratic_vol: 特异性波动率
          - atr_20_norm: 20日ATR归一化（ATR_20/close）
        """
        if stock_daily.empty or index_daily.empty:
            return pd.DataFrame()

        # 保证两边数据日期一致
        common_dates = stock_daily.index.intersection(index_daily.index)
        if len(common_dates) == 0:
            print("警告：股票数据和指数数据没有共同的交易日期")
            return pd.DataFrame()

        stock_daily = stock_daily.loc[common_dates]
        index_daily = index_daily.loc[common_dates]

        # 修复：使用固定窗口大小，确保时间一致性
        # 不再根据数据长度动态调整窗口大小，这会导致不同数据长度产生不同结果
        effective_window = window

        # 检查必要的列是否存在
        required_stock_cols = ['close', 'high', 'low']
        missing_cols = [col for col in required_stock_cols if col not in stock_daily.columns]
        if missing_cols:
            print(f"警告：股票数据缺少必要列: {missing_cols}")
            return pd.DataFrame()

        if 'close' not in index_daily.columns:
            print("警告：指数数据缺少close列")
            return pd.DataFrame()

        # 计算日收益率
        stock_returns = stock_daily['close'].pct_change()
        index_returns = index_daily['close'].pct_change()

        # 修复：使用固定的min_periods，确保时间一致性
        # 不再检查数据长度，让rolling自然处理不足的情况

        # 计算滚动协方差和市场收益率方差
        # 使用固定的min_periods=1，确保时间一致性
        rolling_cov = stock_returns.rolling(effective_window, min_periods=1).cov(index_returns)
        rolling_var = index_returns.rolling(effective_window, min_periods=1).var()

        # 计算贝塔系数，避免除以0
        beta = rolling_cov / rolling_var.replace(0, np.nan)

        # 计算个股收益率的滚动方差，并计算特异性波动率
        stock_rolling_var = stock_returns.rolling(effective_window, min_periods=1).var()
        idiosyncratic_vol = np.sqrt(np.maximum(stock_rolling_var - beta**2 * rolling_var, 0))

        # 修复：使用固定的ATR窗口，确保时间一致性
        atr_window = 20  # 固定20日窗口，不再根据数据长度调整
        high_low = stock_daily['high'] - stock_daily['low']
        high_close = np.abs(stock_daily['high'] - stock_daily['close'].shift(1))
        low_close = np.abs(stock_daily['low'] - stock_daily['close'].shift(1))
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        atr_20 = true_range.rolling(window=atr_window, min_periods=1).mean()
        atr_20_norm = atr_20 / stock_daily['close'].replace(0, np.nan)

        beta_factors = pd.DataFrame(index=stock_daily.index)
        beta_factors['beta'] = beta
        beta_factors['idiosyncratic_vol'] = idiosyncratic_vol
        beta_factors['atr_20_norm'] = atr_20_norm

        return beta_factors

    def calculate_momentum_factors(self, stock_daily: pd.DataFrame) -> pd.DataFrame:
        """
        计算动量因子（对数收益率）：
          - ret_1m: 1个月对数收益率
          - ret_3m: 3个月对数收益率
          - ret_6m: 6个月对数收益率
        """
        if stock_daily.empty:
            return pd.DataFrame()

        # 检查必要的列是否存在
        if 'close' not in stock_daily.columns:
            print("警告：股票数据缺少close列，无法计算动量因子")
            return pd.DataFrame()

        # 修复：移除数据长度检查，确保时间一致性
        # 不再根据数据长度决定是否计算因子，让shift自然处理不足的情况

        # 计算对数价格，处理可能的0值或负值
        close_prices = stock_daily['close'].replace(0, np.nan)
        if close_prices.isna().all():
            print("警告：所有收盘价都为0或NaN，无法计算动量因子")
            return pd.DataFrame()

        log_price = np.log(close_prices)

        momentum_factors = pd.DataFrame(index=stock_daily.index)

        # 修复：始终计算所有动量因子，不依赖数据长度
        # 对于数据不足的情况，shift会自然返回NaN
        momentum_factors['ret_1m'] = log_price - log_price.shift(22)   # 1个月
        momentum_factors['ret_3m'] = log_price - log_price.shift(66)   # 3个月
        momentum_factors['ret_6m'] = log_price - log_price.shift(132)  # 6个月

        return momentum_factors





    def calculate_value_factors(self, daily_basic: pd.DataFrame) -> pd.DataFrame:
        """
        计算价值因子（仅保留TTM口径）：
          - pe_ttm: 市盈率 TTM
          - pb: 市净率
          - ps_ttm: 市销率 TTM
          - dv_ttm: 股息率 TTM
        """
        if daily_basic.empty:
            return pd.DataFrame()

        value_factors = pd.DataFrame()
        if 'pe_ttm' in daily_basic.columns:
            value_factors['pe_ttm'] = daily_basic['pe_ttm']
        if 'pb' in daily_basic.columns:
            value_factors['pb'] = daily_basic['pb']
        if 'ps_ttm' in daily_basic.columns:
            value_factors['ps_ttm'] = daily_basic['ps_ttm']
        if 'dv_ttm' in daily_basic.columns:
            value_factors['dv_ttm'] = daily_basic['dv_ttm']

        return value_factors

    def calculate_advanced_value_factors(self, daily_basic: pd.DataFrame, financial_data: pd.DataFrame) -> pd.DataFrame:
        """
        计算高级估值因子：
          - EY: 盈利收益率 (1/pe_ttm)
          - INV_PB: 市净率倒数 (1/pb)
          - SY: 销售收益率 (1/ps_ttm)
          - DYR: 股息率 (dv_ttm，负值强制为0)
          - ValueScore: 多指标等权综合评分
          - PEG: 市盈率相对盈利增长比率
        """
        if daily_basic.empty:
            return pd.DataFrame()

        advanced_value_factors = pd.DataFrame(index=daily_basic.index)

        # 基础估值因子倒数，先处理None值
        if 'pe_ttm' in daily_basic.columns:
            pe_ttm_clean = pd.to_numeric(daily_basic['pe_ttm'], errors='coerce')
            advanced_value_factors['EY'] = 1.0 / pe_ttm_clean.replace(0, np.nan)

        if 'pb' in daily_basic.columns:
            pb_clean = pd.to_numeric(daily_basic['pb'], errors='coerce')
            advanced_value_factors['INV_PB'] = 1.0 / pb_clean.replace(0, np.nan)

        if 'ps_ttm' in daily_basic.columns:
            ps_ttm_clean = pd.to_numeric(daily_basic['ps_ttm'], errors='coerce')
            advanced_value_factors['SY'] = 1.0 / ps_ttm_clean.replace(0, np.nan)

        if 'dv_ttm' in daily_basic.columns:
            # 股息率，负值强制为0，先处理None值
            dv_ttm_clean = pd.to_numeric(daily_basic['dv_ttm'], errors='coerce')
            advanced_value_factors['DYR'] = np.maximum(dv_ttm_clean.fillna(0), 0)

        # ValueScore: 多指标等权综合评分 - 修复：使用滚动统计确保时间一致性
        value_components = []
        for col in ['EY', 'INV_PB', 'SY', 'DYR']:
            if col in advanced_value_factors.columns:
                # 修复：使用滚动排名而不是全局排名
                # 使用252个交易日（约1年）的滚动窗口
                ranked = advanced_value_factors[col].rolling(window=252, min_periods=1).rank(pct=True)
                value_components.append(ranked)

        if value_components:
            # 等权平均后使用滚动标准化
            value_score_raw = pd.concat(value_components, axis=1).mean(axis=1, skipna=True)
            # 修复：使用滚动均值和标准差，确保时间一致性
            rolling_mean = value_score_raw.rolling(window=252, min_periods=1).mean()
            rolling_std = value_score_raw.rolling(window=252, min_periods=1).std()
            advanced_value_factors['ValueScore'] = (value_score_raw - rolling_mean) / rolling_std.replace(0, np.nan)

        # PEG: 需要计算三年营收复合增长率，修复前瞻性偏差
        if 'pe_ttm' in daily_basic.columns and not financial_data.empty and 'revenue' in financial_data.columns:
            # 首先将财务数据与日线数据对齐，避免前瞻性偏差
            aligned_financial = self._align_financial_data_with_daily(financial_data, daily_basic.index)

            if not aligned_financial.empty and 'revenue' in aligned_financial.columns:
                revenue_data = aligned_financial['revenue'].dropna()
                if len(revenue_data) >= 4:  # 至少需要4个季度数据
                    # 修复：使用向量化的滚动计算，避免基于位置的循环
                    # 计算滚动三年CAGR，使用时间窗口而不是位置窗口

                    # 使用滚动窗口计算CAGR
                    def calculate_rolling_cagr(series, window_quarters=12):
                        """计算滚动CAGR，使用固定的季度窗口"""
                        # 计算最近4个季度的平均值（滚动）
                        recent_avg = series.rolling(window=4, min_periods=1).mean()
                        # 计算3年前4个季度的平均值（向前偏移12个季度后滚动）
                        old_avg = series.shift(12).rolling(window=4, min_periods=1).mean()

                        # 计算CAGR
                        cagr = (recent_avg / old_avg.replace(0, np.nan)) ** (1/3) - 1
                        return cagr

                    # 使用向量化计算替代循环
                    rev_cagr3y = calculate_rolling_cagr(revenue_data)

                    # 修复：确保CAGR数据与daily_basic索引对齐
                    rev_cagr3y_aligned = rev_cagr3y.reindex(daily_basic.index)

                    # 计算PEG
                    advanced_value_factors['PEG'] = daily_basic['pe_ttm'] / (rev_cagr3y_aligned + 1e-9)

        return advanced_value_factors

    def calculate_chip_factors(self, cyq_perf: pd.DataFrame, daily_data: pd.DataFrame) -> pd.DataFrame:
        """
        计算筹码系因子：
          - Chip_Concentration: 筹码集中度 log((cost_85pct - cost_15pct) / cost_50pct)
          - Chip_Bias: 现价偏离筹码均价 (close - weight_avg) / weight_avg
          - Winner: 获利盘比例 winner_rate

        如果筹码分布数据不足，将使用技术指标替代：
          - Chip_Concentration_Alt: 基于价格波动的集中度替代指标
          - Chip_Bias_Alt: 基于移动平均的偏离度替代指标
          - Winner_Alt: 基于价格位置的获利盘替代指标
        """
        if cyq_perf.empty:
            # 如果没有筹码分布数据，使用技术指标替代
            return self._calculate_alternative_chip_factors(daily_data)

        chip_factors = pd.DataFrame(index=cyq_perf.index)

        # 筹码集中度
        if all(col in cyq_perf.columns for col in ['cost_85pct', 'cost_15pct', 'cost_50pct']):
            cost_85_clean = pd.to_numeric(cyq_perf['cost_85pct'], errors='coerce')
            cost_15_clean = pd.to_numeric(cyq_perf['cost_15pct'], errors='coerce')
            cost_50_clean = pd.to_numeric(cyq_perf['cost_50pct'], errors='coerce')
            concentration_raw = (cost_85_clean - cost_15_clean) / cost_50_clean.replace(0, np.nan)
            chip_factors['Chip_Concentration'] = np.log(concentration_raw.replace(0, np.nan))

        # 现价偏离筹码均价
        if 'weight_avg' in cyq_perf.columns and not daily_data.empty and 'close' in daily_data.columns:
            # 确保两个DataFrame都有正确的日期列名
            cyq_reset = cyq_perf.reset_index()
            daily_reset = daily_data[['close']].reset_index()

            # 确保日期列名一致
            date_col_cyq = cyq_reset.columns[0]  # 第一列应该是日期
            date_col_daily = daily_reset.columns[0]  # 第一列应该是日期

            # 重命名为统一的列名
            cyq_reset = cyq_reset.rename(columns={date_col_cyq: 'trade_date'})
            daily_reset = daily_reset.rename(columns={date_col_daily: 'trade_date'})

            # 确保日期列是datetime类型并排序
            cyq_reset['trade_date'] = pd.to_datetime(cyq_reset['trade_date'])
            daily_reset['trade_date'] = pd.to_datetime(daily_reset['trade_date'])
            cyq_reset = cyq_reset.sort_values('trade_date')
            daily_reset = daily_reset.sort_values('trade_date')

            # 使用正确的merge_asof参数
            merged_data = pd.merge_asof(
                cyq_reset,
                daily_reset,
                left_on='trade_date',
                right_on='trade_date',
                direction='backward'
            ).set_index('trade_date')

            # 重新索引到原始cyq_perf的索引
            merged_data = merged_data.reindex(cyq_perf.index)

            close_clean = pd.to_numeric(merged_data['close'], errors='coerce')
            weight_avg_clean = pd.to_numeric(cyq_perf['weight_avg'], errors='coerce')
            chip_factors['Chip_Bias'] = (close_clean - weight_avg_clean) / weight_avg_clean.replace(0, np.nan)

        # 获利盘比例
        if 'winner_rate' in cyq_perf.columns:
            chip_factors['Winner'] = cyq_perf['winner_rate']

        return chip_factors

    def _calculate_alternative_chip_factors(self, daily_data: pd.DataFrame) -> pd.DataFrame:
        """
        当筹码分布数据不可用时，使用技术指标计算替代的筹码因子
        """
        if daily_data.empty or 'close' not in daily_data.columns:
            return pd.DataFrame()

        chip_factors = pd.DataFrame(index=daily_data.index)

        # 检查必要的列
        required_cols = ['high', 'low', 'close', 'vol']
        available_cols = [col for col in required_cols if col in daily_data.columns]

        if len(available_cols) < 3:  # 至少需要high, low, close
            print(f"警告：日线数据缺少必要列，无法计算替代筹码因子。可用列: {available_cols}")
            return pd.DataFrame()

        close = daily_data['close']
        high = daily_data['high'] if 'high' in daily_data.columns else close
        low = daily_data['low'] if 'low' in daily_data.columns else close
        volume = daily_data['vol'] if 'vol' in daily_data.columns else pd.Series(1, index=daily_data.index)

        # 替代筹码集中度：基于价格波动的集中度
        # 使用20日内高低价差相对于收盘价的比例，取对数
        price_range_20 = (high.rolling(20, min_periods=1).max() - low.rolling(20, min_periods=1).min())
        price_range_ratio = price_range_20 / close.replace(0, np.nan)
        chip_factors['Chip_Concentration'] = -np.log(price_range_ratio.replace(0, np.nan))  # 负号使得集中度高时值大

        # 替代筹码偏离度：当前价格相对于成交量加权平均价格的偏离
        # 使用20日VWAP作为筹码均价的替代
        vwap_20 = (close * volume).rolling(20, min_periods=1).sum() / volume.rolling(20, min_periods=1).sum()
        chip_factors['Chip_Bias'] = (close - vwap_20) / vwap_20.replace(0, np.nan)

        # 修复：使用更稳定的向量化方法，避免rolling.apply的不确定性
        # 计算当前价格在过去60日价格分布中的位置
        def stable_rolling_percentile(series, window=60):
            # 使用更稳定的方法：rolling rank
            # 计算每个值在其滚动窗口中的排名百分位
            rolling_rank = series.rolling(window=window, min_periods=1).rank(pct=True)
            # 由于rank计算的是当前值在窗口中的位置，这正是我们需要的
            return rolling_rank

        chip_factors['Winner'] = stable_rolling_percentile(close, window=60)

        return chip_factors

    def calculate_liquidity_factors(self, daily_basic: pd.DataFrame, advanced_value_factors: pd.DataFrame) -> pd.DataFrame:
        """
        计算流动性&大小交互因子：
          - Free_Float_Ratio: 流通股占比 circ_mv / total_mv
          - SmallLowValue: 小市值&低估 -z(ln_total_mv) + ValueScore
        """
        if daily_basic.empty:
            return pd.DataFrame()

        liquidity_factors = pd.DataFrame(index=daily_basic.index)
        
        # 流通股占比
        if 'circ_mv' in daily_basic.columns and 'total_mv' in daily_basic.columns:
            circ_mv_clean = pd.to_numeric(daily_basic['circ_mv'], errors='coerce')
            total_mv_clean = pd.to_numeric(daily_basic['total_mv'], errors='coerce')
            liquidity_factors['Free_Float_Ratio'] = circ_mv_clean / total_mv_clean.replace(0, np.nan)

        # 小市值&低估交互因子 - 修复：使用滚动统计确保时间一致性
        if 'total_mv' in daily_basic.columns and not advanced_value_factors.empty and 'ValueScore' in advanced_value_factors.columns:
            # 修复：计算ln_total_mv的滚动z-score
            total_mv_clean = pd.to_numeric(daily_basic['total_mv'], errors='coerce')
            ln_total_mv = np.log(total_mv_clean.replace(0, np.nan))

            # 使用滚动统计计算z-score，确保时间一致性
            rolling_mean = ln_total_mv.rolling(window=252, min_periods=1).mean()
            rolling_std = ln_total_mv.rolling(window=252, min_periods=1).std()
            ln_total_mv_zscore = (ln_total_mv - rolling_mean) / rolling_std.replace(0, np.nan)

            # 合并ValueScore数据
            value_score_aligned = advanced_value_factors['ValueScore'].reindex(daily_basic.index)

            # 计算SmallLowValue
            liquidity_factors['SmallLowValue'] = -ln_total_mv_zscore + value_score_aligned
        
        return liquidity_factors

    # -------------------- 数据预处理与衍生指标 -------------------- #

    def _robust_fillna(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        更robust的缺失值填充策略：
        1. 首先尝试前向填充
        2. 然后尝试后向填充
        3. 对于仍然为NaN的值，使用列的中位数填充
        4. 如果列全为NaN，则填充为0
        """
        if df.empty:
            return df

        # 前向填充和后向填充
        df_filled = df.fillna(method='ffill').fillna(method='bfill')

        # 对于仍然为NaN的列，使用中位数填充
        for col in df_filled.columns:
            if df_filled[col].isna().any():
                median_val = df_filled[col].median()
                if pd.isna(median_val):
                    # 如果中位数也是NaN（全NaN列），则填充为0
                    df_filled[col] = df_filled[col].fillna(0)
                else:
                    df_filled[col] = df_filled[col].fillna(median_val)

        return df_filled


    

    


    def standardize_and_winsorize(self, df: pd.DataFrame, 
                                          lower_quantile: float = 0.05, 
                                          upper_quantile: float = 0.95, 
                                          window_size: int = 252*10) -> pd.DataFrame:
        """
        改进版本：确保时间一致性的标准化方法
        
        核心改进：
        1. 使用min_periods=1确保rolling在数据不足时也能计算，避免fillna逻辑
        2. 移除expanding/rolling组合逻辑，简化实现
        3. 保持向量化操作，提高性能
        4. 确保固定窗口大小，同一时间点使用相同的历史窗口长度
        
        关键特性：
        - 在相同起始时间但不同截止时间的情况下，时间相交部分的数据完全一致
        - 不依赖数据总长度，只依赖历史窗口大小
        - 向量化实现，性能优于循环版本
        
        Args:
            df: 待处理的因子数据
            lower_quantile: 下分位数，用于winsorize
            upper_quantile: 上分位数，用于winsorize  
            window_size: 固定时间窗口大小（交易日数），默认252*4天（约4年）
        """
        if df.empty:
            return df

        def vectorized_process(series):
            # 检查是否为全NaN列
            if series.isna().all():
                return pd.Series(0, index=series.index)

            # 检查是否有足够的非NaN值
            non_nan_count = series.notna().sum()
            if non_nan_count < 2:
                print(f"警告：列 '{series.name}' 只有 {non_nan_count} 个非NaN值，将使用简单填充")
                return series.fillna(0)

            # 缺失值处理：使用固定窗口rolling中位数填充，确保时间一致性
            # 使用相同的window_size确保不同时间范围的数据填充结果一致
            series_imputed = series.fillna(
                series.rolling(window=window_size, min_periods=1).median()
            )
            
            # 再次检查填充后是否还有NaN
            if series_imputed.isna().any():
                series_imputed = series_imputed.fillna(0)
            
            # 使用向量化的rolling操作，但确保窗口大小固定
            # 关键：使用min_periods=1，让rolling在数据不足时也能计算
            rolling_quantile_lower = series_imputed.rolling(
                window=window_size, min_periods=1
            ).quantile(lower_quantile)
            
            rolling_quantile_upper = series_imputed.rolling(
                window=window_size, min_periods=1
            ).quantile(upper_quantile)
            
            # Winsorize：截尾处理
            winsorized = series_imputed.clip(
                lower=rolling_quantile_lower, 
                upper=rolling_quantile_upper
            )
            
            # 标准化：计算rolling均值和标准差
            rolling_mean = winsorized.rolling(window=window_size, min_periods=1).mean()
            rolling_std = winsorized.rolling(window=window_size, min_periods=1).std()
            
            # 避免除零：标准差为0或NaN时替换为1
            rolling_std = rolling_std.replace(0, 1).fillna(1)
            
            # 标准化计算
            standardized = (winsorized - rolling_mean) / rolling_std
            
            # 填充任何剩余的NaN值
            return standardized.fillna(0)
        
        return df.apply(vectorized_process, axis=0)

    def add_derivative_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        为每个因子计算日变化率（百分比变化），作为衍生指标反映因子变化速度。
        生成的新列名称为原列名加后缀 "_pct_change"。
        对于首行产生的缺失值，采用 0 填充。
        """
        df_deriv = df.pct_change().add_suffix('_pct_change')
        df_deriv.fillna(0, inplace=True)
        return df_deriv

    def smooth_factors(self, df: pd.DataFrame, window: int = 3) -> pd.DataFrame:
        """
        对因子数据进行平滑处理，计算过去 window 个交易日的滚动均值（仅使用过去数据）。
        生成的新列名称为原列名加后缀 "_smooth"。
        """
        df_smooth = df.rolling(window, min_periods=1).mean().add_suffix('_smooth')
        return df_smooth

    def calculate_stk_factor_pro_factors(self, stk_factor_pro_data: pd.DataFrame) -> pd.DataFrame:
        """
        计算股票因子专业版相关因子，包括：
          - pe: 市盈率
          - pe_ttm: 市盈率TTM
          - pb: 市净率
          - ps: 市销率
          - ps_ttm: 市销率TTM
          - dv_ratio: 股息率
          - dv_ttm: 股息率TTM
          - free_share: 自由流通股本
          - total_mv: 总市值
          - circ_mv: 流通市值
        """
        if stk_factor_pro_data.empty:
            return pd.DataFrame()

        factor_columns = ['pe', 'pe_ttm', 'pb', 'ps', 'ps_ttm', 'dv_ratio', 'dv_ttm', 'free_share', 'total_mv', 'circ_mv']
        available_columns = [col for col in factor_columns if col in stk_factor_pro_data.columns]
        
        if available_columns:
            return stk_factor_pro_data[available_columns]
        return pd.DataFrame()

    def calculate_cyq_perf_factors(self, cyq_perf_data: pd.DataFrame) -> pd.DataFrame:
        """
        计算筹码分布相关因子，包括：
          - cost_5pct: 5%成本分布
          - cost_15pct: 15%成本分布
          - cost_50pct: 50%成本分布
          - cost_85pct: 85%成本分布
          - cost_95pct: 95%成本分布
          - weight_avg: 加权平均成本
          - winner_rate: 获利盘比例
        """
        if cyq_perf_data.empty:
            return pd.DataFrame()

        factor_columns = ['cost_5pct', 'cost_15pct', 'cost_50pct', 'cost_85pct', 'cost_95pct', 'weight_avg', 'winner_rate']
        available_columns = [col for col in factor_columns if col in cyq_perf_data.columns]
        
        if available_columns:
            return cyq_perf_data[available_columns]
        return pd.DataFrame()

    def get_all_factors(self, ts_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取指定股票在某一日期区间内的所有因子数据，涵盖规模、贝塔、动量、价值、高级估值、筹码、流动性和股票因子专业版因子。
        流程：
          1. 获取个股日线、每日基本指标、市场指数、财务数据、股票因子专业版数据和筹码分布数据
          2. 分别计算各类因子
          3. 合并各类因子（按日期对齐）
          4. 对合并后的因子数据先进行缺失值填补、异常值截尾及标准化处理
          5. 基于处理后的数据生成衍生特征（变化率）和平滑特征
          6. 将原始因子、变化率和平滑后的因子合并，作为最终传入模型的数据
        """
        # 1. 获取原始数据
        stock_daily = self._fetch_daily_data(ts_code, start_date, end_date)
        daily_basic = self._fetch_daily_basic(ts_code, start_date, end_date)
        index_daily = self._fetch_index_daily(config_info['market'], start_date, end_date)
        financial_data = self._fetch_financial_data(ts_code, start_date, end_date)
        # stk_factor_pro_data = self._fetch_stk_factor_pro(ts_code, start_date, end_date)
        cyq_perf_data = self._fetch_cyq_perf(ts_code, start_date, end_date)

        # 2. 分别计算各类因子
        size_factors = self.calculate_size_factors(daily_basic)
        beta_factors = self.calculate_beta_factors(stock_daily, index_daily)
        momentum_factors = self.calculate_momentum_factors(stock_daily)
        value_factors = self.calculate_value_factors(daily_basic)
        advanced_value_factors = self.calculate_advanced_value_factors(daily_basic, financial_data)

        # 检查筹码数据覆盖率，如果覆盖率太低则使用替代方法
        if not cyq_perf_data.empty and not stock_daily.empty:
            coverage_ratio = len(cyq_perf_data) / len(stock_daily)
            if coverage_ratio < 0.5:  # 如果筹码数据覆盖率低于50%
                chip_factors = self._calculate_alternative_chip_factors(stock_daily)
            else:
                chip_factors = self.calculate_chip_factors(cyq_perf_data, stock_daily)
        else:
            chip_factors = self.calculate_chip_factors(cyq_perf_data, stock_daily)

        liquidity_factors = self.calculate_liquidity_factors(daily_basic, advanced_value_factors)
        # stk_factor_pro_factors = self.calculate_stk_factor_pro_factors(stk_factor_pro_data)
        # cyq_perf_factors = self.calculate_cyq_perf_factors(cyq_perf_data)

        # 3. 改进的因子合并逻辑，确保索引对齐
        # 确定基准日期索引（优先使用stock_daily，其次是daily_basic）
        if not stock_daily.empty:
            base_index = stock_daily.index
        elif not daily_basic.empty:
            base_index = daily_basic.index
        else:
            print(f"警告：股票 {ts_code} 没有可用的基础数据")
            return pd.DataFrame()

        # 收集所有非空的因子DataFrame并重新索引到基准索引
        factors_list = []
        factor_dfs = [
            ('size_factors', size_factors),
            ('beta_factors', beta_factors),
            ('momentum_factors', momentum_factors),
            ('value_factors', value_factors),
            ('advanced_value_factors', advanced_value_factors),
            ('chip_factors', chip_factors),
            ('liquidity_factors', liquidity_factors)
        ]

        for name, factor_df in factor_dfs:
            if not factor_df.empty:
                # 重新索引到基准索引，缺失的日期用NaN填充
                aligned_df = factor_df.reindex(base_index)
                factors_list.append(aligned_df)

        if not factors_list:
            print(f"警告：股票 {ts_code} 没有计算出任何因子")
            return pd.DataFrame()

        # 合并所有对齐后的因子
        factors = pd.concat(factors_list, axis=1)
        factors = factors[~factors.index.duplicated(keep='first')].sort_index()

        # 4. 缺失值填补、异常值截尾及标准化（均在一个函数中完成）
        # 使用改进版本确保时间一致性：相同起始时间但不同截止时间的情况下，时间相交部分数据完全一致
        # 核心改进：使用min_periods=1的rolling操作，避免expanding/rolling组合逻辑
        factors = self.standardize_and_winsorize(factors)

        # 5. 生成衍生特征：计算日百分比变化率（变化率）和滚动均值（平滑特征）
        # 使用更robust的缺失值填充策略
        # factors_imputed = self._robust_fillna(factors)
        # factors_deriv = self.add_derivative_features(factors_imputed)
        # factors_smooth = self.smooth_factors(factors_imputed)

        # 6. 合并原始因子、变化率和平滑后的因子
        # 确保没有重复的列名
        # factors_final = pd.concat([factors, factors_deriv, factors_smooth], axis=1)

        # 去除可能的重复列
        factors_final = factors.loc[:, ~factors.columns.duplicated()]

        return factors_final

    def clear_cache(self):
        """清空所有缓存数据"""
        if not self._is_cache_enabled():
            print("缓存功能已禁用，无需清空缓存")
            return
            
        self.cached_data.clear()
        self.cache_access_time.clear()

        # 删除缓存文件
        cache_file = os.path.join(self.cache_dir, 'factor_cache.pkl')
        access_time_file = os.path.join(self.cache_dir, 'cache_access_time.pkl')

        for file_path in [cache_file, access_time_file]:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except Exception as e:
                    print(f"删除缓存文件失败 {file_path}: {e}")

    def get_cache_info(self):
        """获取缓存统计信息"""
        if not self._is_cache_enabled():
            return {
                'cache_enabled': False,
                'message': '缓存功能已禁用（环境变量use_cache未设置为true）'
            }
            
        total_size = len(self.cached_data)
        data_types = {}

        for cache_info in self.cached_data.values():
            data_type = cache_info['data_type']
            data_types[data_type] = data_types.get(data_type, 0) + 1

        return {
            'cache_enabled': True,
            'total_entries': total_size,
            'max_size': self.max_cache_size,
            'data_types': data_types,
            'cache_dir': self.cache_dir
        }

    def _remove_white_border(self, img):
        """去除图片的白边"""
        if img.mode in ("RGB", "RGBA"):
            white_bg = Image.new(img.mode, img.size, (255, 255, 255))
        else:
            white_bg = Image.new(img.mode, img.size, 255)

        diff = ImageChops.difference(img, white_bg)
        bbox = diff.getbbox()
        if bbox:
            return img.crop(bbox)
        else:
            return img

    def _analyze_single_factor(self, factor_data, close_data, col_name, periods=(1, 5, 10), quantiles=5, max_loss=0.35):
        """分析单个因子，参考analyze_and_plot_factor函数，智能调整max_loss阈值"""
        if not ALPHALENS_AVAILABLE:
            print(f"跳过因子 {col_name} 的分析：alphalens库不可用")
            return

        print(f'-------{col_name}-------------')

        # 对于筹码相关因子，使用更高的max_loss阈值
        chip_related_factors = ['Chip_Concentration', 'Chip_Bias', 'Winner']
        if any(chip_factor in col_name for chip_factor in chip_related_factors):
            adjusted_max_loss = 0.65  # 对筹码因子使用65%的阈值
        else:
            adjusted_max_loss = max_loss

        try:
            factors = alphalens.utils.get_clean_factor_and_forward_returns(
                factor_data,
                close_data,
                bins=None,
                periods=periods,
                quantiles=quantiles,
                max_loss=adjusted_max_loss
            )

            if factors.empty:
                print(f"Factors for {col_name} are empty. Skipping...")
                return

            print(factors.head())
            ic = alphalens.performance.mean_information_coefficient(factors)
            print(f"Information Coefficient for {col_name}:")
            print(ic)
            print("Creating returns tear sheet...")

            # 创建输出目录
            if not os.path.exists('alphas_fig'):
                os.makedirs('alphas_fig')

            def save_all_figs():
                fig = plt.figure(2)
                fig.savefig(f'alphas_fig/{col_name}.png')

            # 保存原始的plt函数
            original_show = plt.show
            original_close = plt.close
            plt.show = save_all_figs
            plt.close = lambda *args, **kwargs: None

            # 创建因子分析图表
            create_returns_tear_sheet(factors, long_short=False)

            # 恢复原始的plt函数
            plt.show = original_show
            plt.close = original_close

            print("Tear sheet created.")
            plt.close('all')

            # 去除图片白边
            img_path = f'alphas_fig/{col_name}.png'
            if os.path.exists(img_path):
                try:
                    with Image.open(img_path) as img:
                        cropped_img = self._remove_white_border(img)
                        cropped_img.save(img_path)
                except Exception as e:
                    print(f"处理图片 {img_path} 时出错: {e}")

        except Exception as e:
            print(f'分析因子 {col_name} 时出错: {e}')

    def analyze_all_factors(self, pkl_file_path, start_date=None, end_date=None, 
                           periods=(1, 5, 10), quantiles=5, max_loss=0.35,
                           exclude_columns=None):
        """
        加载pkl文件并分析所有因子
        
        Args:
            pkl_file_path: pkl文件路径
            start_date: 分析开始日期，格式'YYYYMMDD'
            end_date: 分析结束日期，格式'YYYYMMDD'
            periods: 分析周期
            quantiles: 分位数
            max_loss: 最大损失阈值
            exclude_columns: 要排除的列名列表
        """
        if not ALPHALENS_AVAILABLE:
            print("错误：alphalens库不可用，无法进行因子分析")
            return

        if not os.path.exists(pkl_file_path):
            print(f"错误：文件 {pkl_file_path} 不存在")
            return

        print(f"正在加载文件: {pkl_file_path}")
        try:
            df = pd.read_pickle(pkl_file_path)
            print(f"数据形状: {df.shape}")
            print(f"数据索引: {df.index.names}")
            print(f"数据列数: {len(df.columns)}")
        except Exception as e:
            print(f"加载文件失败: {e}")
            return

        # 筛选日期范围
        if start_date or end_date:
            if start_date:
                start_dt = pd.to_datetime(start_date)
                df = df[df.index.get_level_values(0) >= start_dt]
            if end_date:
                end_dt = pd.to_datetime(end_date)
                df = df[df.index.get_level_values(0) <= end_dt]
            print(f"筛选日期后数据形状: {df.shape}")

        if df.empty:
            print("错误：数据为空")
            return

        # 准备价格数据
        if 'close' not in df.columns:
            print("警告：因子文件中缺少'close'列，尝试从原始数据文件加载价格数据...")
            try:
                # 尝试从当前目录找到原始数据文件
                original_file = None
                current_dir = os.path.dirname(pkl_file_path) or '.'
                for file in os.listdir(current_dir):
                    if file.startswith('refined_') and file.endswith('.pkl') and file != os.path.basename(pkl_file_path):
                        original_file = os.path.join(current_dir, file)
                        break
                
                if not original_file:
                    print("错误：未找到原始数据文件（refined_*.pkl）")
                    return
                if os.path.exists(original_file):
                    print(f"正在从 {original_file} 加载价格数据...")
                    original_df = pd.read_pickle(original_file)
                    if 'close' in original_df.columns:
                        # 筛选相同的日期和股票范围
                        common_index = df.index.intersection(original_df.index)
                        if len(common_index) > 0:
                            close_data = original_df.loc[common_index, 'close'].unstack()
                            print(f"成功加载价格数据，形状: {close_data.shape}")
                        else:
                            print("错误：因子数据和原始数据没有共同的索引")
                            return
                    else:
                        print(f"错误：原始数据文件 {original_file} 中也缺少'close'列")
                        return
                else:
                    print(f"错误：原始数据文件 {original_file} 不存在")
                    return
            except Exception as e:
                print(f"加载价格数据失败: {e}")
                return
        else:
            close_data = df.pivot_table(index='date' if 'date' in df.index.names else df.index.get_level_values(0).name,
                                       columns='sec_id' if 'sec_id' in df.index.names else df.index.get_level_values(1).name,
                                       values='close')

        # 设置要排除的列
        if exclude_columns is None:
            exclude_columns = ['close', 'open', 'high', 'low', 'volume', 'amount', 'vol', 
                             'adjclose', 'return', 'vwap', 'weight']

        # 获取所有因子列
        factor_columns = [col for col in df.columns if col not in exclude_columns]
        print(f"找到 {len(factor_columns)} 个因子列")
        
        if not factor_columns:
            print("警告：没有找到可分析的因子列")
            return

        # 分析每个因子
        successful_analyses = 0
        failed_analyses = 0
        
        for col in tqdm(factor_columns, desc="分析因子"):
            try:
                factor_series = df[col]
                # 检查因子数据是否有效
                if factor_series.isna().all():
                    print(f"跳过因子 {col}：所有值都是NaN")
                    failed_analyses += 1
                    continue
                
                if factor_series.nunique() <= 1:
                    print(f"跳过因子 {col}：值的唯一性不足")
                    failed_analyses += 1
                    continue

                self._analyze_single_factor(factor_series, close_data, col, 
                                          periods=periods, quantiles=quantiles, max_loss=max_loss)
                successful_analyses += 1
                
            except Exception as e:
                print(f'分析因子 {col} 时出错: {e}')
                failed_analyses += 1
                continue

        print(f"\n因子分析完成！")
        print(f"成功分析: {successful_analyses} 个因子")
        print(f"分析失败: {failed_analyses} 个因子")
        print(f"图表保存在: alphas_fig/ 目录")


if __name__ == "__main__":

    def main(start_date, end_date, save_path, analyze_factors=False):
        """
        入口函数，用于测试 get_all_factors 方法：
          1. 读取股票列表
          2. 对每只股票计算各类因子及其衍生指标
          3. 合并因子数据后保存为 pickle 文件
          4. 可选：分析所有因子

        Args:
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            save_path: 保存路径
            analyze_factors: 是否分析因子，默认False

        注意：
          - 对因子数据先做缺失值填补、异常值处理及标准化，
            再生成变化率与平滑特征，确保输入模型数据分布稳定且反映动态变化。
        """
        init_config()
        df = load_stock_data(config_info['alpha_type'], config_info['market'])
        stock_list = df.index.get_level_values(1).unique()
        calculator = FactorCalculator(token=os.environ['TUSHARE'])
        if os.path.exists(save_path):
            factor_df = pd.read_pickle(save_path)
        else:
            factor_df = pd.DataFrame()
        try:
            for code in tqdm(stock_list, desc="Processing stocks"):
                try:
                    if code.startswith('6'):
                        ts_code = f"{code}.SH"
                    elif code.startswith(('0', '1', '2', '3')):
                        ts_code = f"{code}.SZ"
                    elif code.startswith(('87', '83', '4')):
                        ts_code = f"{code}.BJ"
                    else:
                        continue

                    factors = calculator.get_all_factors(ts_code, str(start_date), str(end_date))
                    factors['sec_id'] = code
                    factors.reset_index(inplace=True)
                    factors.rename(columns={'datetime': 'date'}, inplace=True)
                    factors.set_index(['date', 'sec_id'], inplace=True)
                    
                    # 确保合并时没有重复的索引
                    if not factor_df.empty:
                        # 移除factor_df中已存在的相同索引
                        common_index = factor_df.index.intersection(factors.index)
                        if len(common_index) > 0:
                            factor_df = factor_df.drop(index=common_index)
                    
                    factor_df = pd.concat([factor_df, factors])
                except Exception as e:
                    import traceback
                    traceback.print_exc()

                    print(f"处理股票 {code} 时出错: {e}")
                    continue
        except KeyboardInterrupt:
            print("\n用户中断程序，正在保存已处理的数据...")
        except Exception as e:
            print(f"程序执行出错: {e}")
        finally:
            print("正在保存缓存和数据...")
            calculator.save_cache()
            if not factor_df.empty:
                factor_df.to_pickle(save_path)
                print(f"因子数据已保存到: {save_path}")
                
                # 将新计算的因子数据合并到原始df中
                try:
                    print("正在将因子数据合并到原始数据中...")
                    
                    # 获取原始df的文件路径
                    original_file = f'refined_{config_info["alpha_type"]}_{config_info["market"]}.pkl'
                    
                    if os.path.exists(original_file):
                        # 重新加载原始df以确保数据最新
                        original_df = pd.read_pickle(original_file)
                        
                        # 获取factor_df中的所有因子列（排除索引列）
                        factor_columns = factor_df.columns.tolist()
                        
                        # 找到原始df中已存在的因子列
                        existing_factor_cols = [col for col in factor_columns if col in original_df.columns]
                        if existing_factor_cols:
                            print(f"将替换原始数据中的 {len(existing_factor_cols)} 个因子列: {existing_factor_cols[:5]}...")
                            # 删除原有的因子列
                            original_df = original_df.drop(columns=existing_factor_cols)
                        
                        # 合并新的因子数据（只合并索引匹配的部分）
                        # 使用left join确保只保留原始df中存在的数据
                        merged_df = original_df.join(factor_df, how='left')
                        
                        # 保存合并后的数据到原始路径
                        merged_df.to_pickle(original_file)
                        print(f"已将 {len(factor_columns)} 个因子列合并到原始数据并保存到: {original_file}")
                        print(f"合并后数据形状: {merged_df.shape}")
                    else:
                        print(f"原始数据文件 {original_file} 不存在，跳过合并步骤")
                        
                except Exception as e:
                    print(f"合并因子数据到原始df时出错: {e}")
            
            # 打印缓存统计信息
            cache_info = calculator.get_cache_info()
            print(f"缓存统计: {cache_info}")
            
            # 如果需要分析因子
            if analyze_factors and os.path.exists(save_path):
                print("\n开始分析因子...")
                try:
                    calculator.analyze_all_factors(
                        pkl_file_path=save_path,
                        start_date=str(start_date),
                        end_date=str(end_date),
                        periods=(1, 5, 10),
                        quantiles=5,
                        max_loss=0.35
                    )
                except Exception as e:
                    print(f"因子分析过程中出错: {e}")
            elif analyze_factors:
                print(f"警告：无法进行因子分析，文件 {save_path} 不存在")
            
            print("程序执行完成。")

    def analyze_factors_only(pkl_file_path, start_date=None, end_date=None):
        """
        仅分析因子的独立函数
        
        Args:
            pkl_file_path: pkl文件路径
            start_date: 分析开始日期，格式YYYYMMDD，可选
            end_date: 分析结束日期，格式YYYYMMDD，可选
        """
        init_config()
        calculator = FactorCalculator(token=os.environ['TUSHARE'])
        
        try:
            calculator.analyze_all_factors(
                pkl_file_path=pkl_file_path,
                start_date=start_date,
                end_date=end_date,
                periods=(1, 5, 10),
                quantiles=5,
                max_loss=0.35
            )
        except Exception as e:
            print(f"因子分析过程中出错: {e}")

    # 使用argparse处理命令行参数
    parser = argparse.ArgumentParser(description='因子计算和分析工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    today_str = datetime.datetime.now().strftime('%Y%m%d')

    # 主要功能：计算因子
    main_parser = subparsers.add_parser('main', help='计算因子')
    main_parser.add_argument('--start_date', help='开始日期，格式YYYYMMDD')
    main_parser.add_argument('--end_date', nargs='?', default=today_str, help='结束日期，格式YYYYMMDD')
    main_parser.add_argument('--save_path', help='保存路径')
    main_parser.add_argument('--analyze_factors', action='store_true', help='是否分析因子')
    
    # 分析功能：分析因子
    analyze_parser = subparsers.add_parser('analyze', help='分析因子')
    analyze_parser.add_argument('--pkl_file_path', help='pkl文件路径')
    analyze_parser.add_argument('--start_date', help='分析开始日期，格式YYYYMMDD')
    analyze_parser.add_argument('--end_date', nargs='?', default=today_str,help='分析结束日期，格式YYYYMMDD')
    
    args = parser.parse_args()
    
    
    if args.command == 'main':
        main(args.start_date, args.end_date, args.save_path, args.analyze_factors)
    elif args.command == 'analyze':
        analyze_factors_only(args.pkl_file_path, args.start_date, args.end_date)
    else:
        # 如果没有指定子命令，尝试使用fire处理（向后兼容）
        try:
            fire.Fire(main)
        except Exception as e:
            print(f"Fire处理失败: {e}")
            parser.print_help()
