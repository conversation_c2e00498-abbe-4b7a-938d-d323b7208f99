{"system": "<PERSON>", "version": "1.4.0", "lite": false, "py_version": "3.9", "py_version_micro": "3.9.21", "packages": {"orjson": "3.10.16", "shellingham": "1.5.4", "seqeval": "1.2.2", "datasets": "3.5.0", "cymem": "2.0.11", "arrow": "1.3.0", "fastai": "2.7.19", "nlpaug": "1.1.11", "threadpoolctl": "3.6.0", "redis": "5.2.1", "uri-template": "1.3.0", "thinc": "8.2.5", "rfc3339-validator": "0.1.4", "pexpect": "4.9.0", "pygments": "2.19.1", "fire": "0.7.0", "text-unidecode": "1.3", "pandas": "2.2.3", "adagio": "0.2.6", "lazy_loader": "0.4", "langchain-core": "0.1.23", "tomli": "2.0.1", "protobuf": "5.29.4", "fqdn": "1.5.1", "idna": "3.10", "babel": "2.17.0", "ordered-set": "4.1.0", "boto3": "1.37.29", "types-pytz": "2025.2.0.20250516", "traitlets": "5.14.3", "language_data": "1.3.0", "fastprogress": "1.0.3", "nest-asyncio": "1.6.0", "pytorch-lightning": "2.5.1", "markdown": "3.7", "distlib": "0.3.9", "ptyprocess": "0.7.0", "aiohttp": "3.11.16", "alphalens": "0.4.0", "pyexecjs": "1.5.1", "prometheus_client": "0.21.1", "isoduration": "20.11.0", "pillow": "11.1.0", "ppft": "*******", "pytz": "2025.2", "scipy": "1.13.1", "autogluon.timeseries": "1.4.0", "mako": "1.3.9", "matplotlib": "3.9.4", "fake-useragent": "1.5.1", "pydantic": "2.11.2", "plotly": "6.0.1", "overrides": "7.7.0", "parso": "0.8.4", "importlib_metadata": "8.0.0", "webencodings": "0.5.1", "colorama": "0.4.6", "coreforecast": "0.0.12", "uvicorn": "0.34.0", "grpcio": "1.71.0", "urllib3": "1.26.20", "tinycss2": "1.4.0", "openmim": "0.3.9", "zipp": "3.19.2", "fs": "2.4.16", "mlflow": "2.21.3", "pyzmq": "26.4.0", "fonttools": "4.57.0", "msgpack": "1.1.0", "huggingface-hub": "0.30.2", "anyio": "4.9.0", "statsforecast": "1.7.8", "defusedxml": "0.7.1", "graphql-core": "3.2.6", "requests": "2.32.3", "frozenlist": "1.5.0", "graphene": "3.4.3", "openpyxl": "3.1.5", "attrs": "25.3.0", "dnspython": "2.7.0", "termcolor": "3.0.1", "spacy": "3.7.5", "transformers": "4.49.0", "async-lru": "2.0.5", "autogluon.core": "1.4.0", "spacy-legacy": "3.0.12", "blinker": "1.9.0", "soupsieve": "2.6", "ruamel.yaml.clib": "0.2.12", "hyperopt": "0.2.7", "markdown-it-py": "3.0.0", "torchvision": "0.20.1", "mplfinance": "0.12.10b0", "seaborn": "0.13.2", "mypy-extensions": "1.0.0", "h11": "0.14.0", "gitpython": "3.1.44", "pyparsing": "3.2.3", "colorlog": "6.9.0", "gdown": "5.2.0", "pycryptodome": "3.22.0", "jupyter-console": "6.6.3", "jedi": "0.19.2", "spacy-loggers": "1.0.5", "joblib": "1.4.2", "et_xmlfile": "2.0.0", "typing-inspect": "0.9.0", "cloudpathlib": "0.21.0", "einx": "0.3.0", "graphviz": "0.20.3", "jupyterlab": "4.3.6", "rpds-py": "0.24.0", "multidict": "6.3.2", "itsdangerous": "2.2.0", "jupyter": "1.1.1", "pysocks": "1.7.1", "patsy": "1.0.1", "langsmith": "0.0.87", "catalogue": "2.0.10", "jupyter_client": "8.6.3", "autogluon": "1.4.0", "beautifulsoup4": "4.13.3", "narwhals": "1.34.0", "rich": "13.9.4", "asttokens": "3.0.0", "tqdm": "4.67.1", "future": "1.0.0", "jupyterlab_widgets": "3.0.13", "pandocfilters": "1.5.1", "json5": "0.12.0", "utilsforecast": "0.2.4", "fastjsonschema": "2.21.1", "exceptiongroup": "1.2.2", "fastdownload": "0.0.7", "jsonpatch": "1.33", "click": "8.1.8", "typing-inspection": "0.4.0", "jsonpointer": "3.0.0", "tzdata": "2025.2", "appnope": "0.1.4", "autogluon.multimodal": "1.4.0", "langchain-community": "0.0.20", "numpy": "1.26.4", "jupyter_server": "2.15.0", "numba": "0.60.0", "weasel": "0.4.1", "shell_gpt": "1.4.4", "pox": "0.3.5", "decorator": "5.2.1", "packaging": "24.2", "mdit-py-plugins": "0.4.2", "tokenizers": "0.21.1", "appdirs": "1.4.4", "confection": "0.1.5", "pandas-stubs": "2.2.2.240807", "python-redis-lock": "4.0.0", "uc-micro-py": "1.0.3", "cffi": "1.17.1", "virtualenv": "20.30.0", "executing": "2.2.0", "nbformat": "5.10.4", "pyasn1": "0.6.1", "async-timeout": "4.0.3", "stack-data": "0.6.3", "argon2-cffi": "23.1.0", "tensorboard": "2.19.0", "debugpy": "1.8.13", "python-dotenv": "1.1.0", "aiohttp-cors": "0.8.1", "textual": "3.0.1", "lightgbm": "4.5.0", "pyqlib": "0.9.6", "dataclasses-json": "0.6.7", "catboost": "1.2.7", "autogluon.features": "1.4.0", "multiprocess": "0.70.16", "openxlab": "0.0.11", "triad": "0.9.8", "clarabel": "0.10.0", "werkzeug": "3.1.3", "psutil": "6.1.1", "kiwisolver": "1.4.7", "setuptools": "78.1.0", "omegaconf": "2.2.3", "pandas-datareader": "0.10.0", "jupyter_core": "5.7.2", "xxhash": "3.5.0", "torchmetrics": "1.2.1", "nbconvert": "7.16.6", "pip": "25.0", "referencing": "0.36.2", "botocore": "1.37.29", "fugue": "0.9.1", "jmespath": "1.0.1", "gym-notices": "0.0.8", "contourpy": "1.3.0", "regex": "2024.11.6", "loguru": "0.7.3", "fastcore": "1.7.29", "send2trash": "1.8.3", "fsspec": "2024.12.0", "jupyter-lsp": "2.2.5", "graphql-relay": "3.2.0", "opentelemetry-sdk": "1.31.1", "pydash": "7.0.7", "nbclient": "0.10.2", "gitdb": "4.0.12", "deprecated": "1.2.18", "sniffio": "1.3.1", "simplejson": "3.20.1", "langchain": "0.0.354", "jsonschema-specifications": "2024.10.1", "akshare": "1.16.73", "mdurl": "0.1.2", "python-dateutil": "2.9.0.post0", "mpmath": "1.3.0", "jupyterlab_pygments": "0.3.0", "osqp": "1.0.3", "mistune": "3.1.3", "torch": "2.5.1", "py-spy": "0.4.0", "toolz": "0.12.1", "typer": "0.9.4", "gunicorn": "23.0.0", "charset-normalizer": "3.4.1", "preshed": "3.0.9", "py4j": "********", "ipython": "8.18.1", "rsa": "4.9", "bokeh": "2.3.3", "wcwidth": "0.2.13", "scs": "3.2.7.post2", "pywencai": "0.12.3", "widgetsnbextension": "4.0.13", "optuna": "4.2.1", "timm": "1.0.3", "srsly": "2.5.1", "antlr4-python3-runtime": "4.9.3", "nltk": "3.8.1", "opencensus-context": "0.1.3", "jupyter-events": "0.12.0", "html5lib": "1.1", "backtrader": "**********", "certifi": "2025.1.31", "typing_extensions": "4.12.2", "boruta": "0.4.3", "lightning": "2.5.1", "databricks-sdk": "0.49.0", "tenacity": "8.5.0", "lxml": "5.3.2", "evaluate": "0.4.3", "wasabi": "1.1.3", "docstring-parser": "0.15", "platformdirs": "4.2.2", "lightning-utilities": "0.14.3", "fastapi": "0.115.12", "jupyterlab_server": "2.27.3", "markdown2": "2.5.3", "webcolors": "24.11.1", "httpcore": "1.0.7", "tensorboardx": "*******", "xlrd": "2.0.1", "pycparser": "2.22", "cycler": "0.12.1", "ipywidgets": "8.1.5", "jinja2": "3.1.6", "wheel": "0.45.1", "opencensus": "0.11.4", "pytabkit": "1.6.1", "langcodes": "3.5.0", "filelock": "3.18.0", "pyarrow": "19.0.1", "model-index": "0.1.11", "dill": "0.3.8", "comm": "0.2.2", "google-auth": "2.38.0", "marisa-trie": "1.2.1", "statsmodels": "0.14.4", "imageio": "2.37.0", "linkify-it-py": "2.0.3", "pure_eval": "0.2.3", "tornado": "6.4.2", "opentelemetry-api": "1.31.1", "scikit-image": "0.24.0", "mini-racer": "0.12.4", "openai": "1.71.0", "distro": "1.9.0", "cloudpickle": "3.1.1", "s3transfer": "0.11.4", "pydantic_core": "2.33.1", "xgboost": "2.1.4", "starlette": "0.46.1", "rfc3986-validator": "0.1.1", "ray": "2.39.0", "aiohappyeyeballs": "2.6.1", "python-json-logger": "3.3.0", "smart-open": "7.1.0", "gluonts": "0.16.1", "terminado": "0.18.1", "google-api-core": "2.24.2", "wrapt": "1.17.2", "docker": "7.1.0", "networkx": "3.2.1", "pytorch-metric-learning": "2.3.0", "flask": "3.1.0", "propcache": "0.3.1", "instructor": "0.4.8", "backtrader-plotting": "2.0.0", "sympy": "1.13.1", "argon2-cffi-bindings": "21.2.0", "bs4": "0.0.2", "einops": "0.8.1", "aiosignal": "1.3.2", "pyyaml": "6.0.2", "httpx": "0.28.1", "murmurhash": "1.0.12", "matplotlib-inline": "0.1.7", "smmap": "5.0.2", "jsonpath": "0.82.2", "sentencepiece": "0.2.0", "safetensors": "0.5.3", "sqlparse": "0.5.3", "sqlalchemy": "2.0.40", "nvidia-ml-py3": "7.352.0", "empyrical": "0.5.5", "pyasn1_modules": "0.4.2", "tensorboard-data-server": "0.7.2", "pdf2image": "1.17.0", "notebook": "7.3.3", "cvxpy": "1.6.4", "autogluon.tabular": "1.4.0", "jupyter_server_terminals": "0.5.3", "types-python-dateutil": "2.9.0.20241206", "bleach": "6.2.0", "tushare": "1.4.21", "prompt_toolkit": "3.0.50", "memray": "1.17.0", "tifffile": "2024.8.30", "window_ops": "0.0.15", "googleapis-common-protos": "1.69.2", "ipykernel": "6.29.5", "mlflow-skinny": "2.21.3", "pathos": "0.3.3", "marshmallow": "3.26.1", "colorful": "0.5.6", "frozendict": "2.4.6", "gym": "0.26.2", "annotated-types": "0.7.0", "yarl": "1.19.0", "scikit-learn": "1.5.2", "jsonschema": "4.21.1", "opentelemetry-semantic-conventions": "0.52b1", "cachetools": "5.5.2", "alembic": "1.15.2", "accelerate": "0.34.2", "six": "1.17.0", "mlforecast": "0.14.0", "opendatalab": "0.0.10", "proto-plus": "1.26.1", "jiter": "0.9.0", "importlib_resources": "6.5.2", "pytesseract": "0.3.10", "ruamel.yaml": "0.18.10", "autogluon.common": "1.4.0", "tabulate": "0.9.0", "notebook_shim": "0.2.4", "llvmlite": "0.43.0", "pymongo": "4.11.3", "absl-py": "2.2.2", "blis": "0.7.11", "websocket-client": "1.8.0", "markupsafe": "3.0.2", "jaraco.text": "3.12.1", "jaraco.collections": "5.1.0", "jaraco.functools": "4.0.1", "jaraco.context": "5.3.0", "inflect": "7.3.1", "autocommand": "2.2.2", "backports.tarfile": "1.2.0", "typeguard": "4.3.0", "more-itertools": "10.3.0"}}